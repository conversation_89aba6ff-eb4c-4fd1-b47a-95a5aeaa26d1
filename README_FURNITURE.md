# دليل استخدام ميزة إضافة الأثاث

## الميزات الجديدة المضافة

تم إضافة إمكانية إضافة كائنات ثلاثية الأبعاد مثل الكراسي والطاولات إلى التطبيق.

### الأزرار الجديدة في الشريط الجانبي:

#### 🪑 زر الكرسي (CHAIR)
- **الوظيفة**: إضافة كرسي واقعي إلى التصميم
- **اللون**: بني داكن
- **النموذج**: يستخدم ملف `chair.obj` للحصول على شكل واقعي
- **حقل الإدخال**: حجم الكرسي بالمتر (افتراضي: 0.5 متر)
- **طريقة الاستخدام**:
  1. اختر أداة الكرسي من الشريط الجانبي (أيقونة مع "3D")
  2. أدخل حجم الكرسي المطلوب في حقل الإدخال
  3. انقر في أي مكان في منطقة الرسم لوضع الكرسي

#### 🪑 زر الطاولة (TABLE)
- **الوظيفة**: إضافة طاولة إلى التصميم
- **اللون**: بني فاتح
- **حقول الإدخال**:
  - العرض (W): عرض الطاولة بالمتر (افتراضي: 1.2 متر)
  - الطول (L): طول الطاولة بالمتر (افتراضي: 2.0 متر)
- **طريقة الاستخدام**:
  1. اختر أداة الطاولة من الشريط الجانبي
  2. أدخل عرض وطول الطاولة في حقول الإدخال
  3. انقر في أي مكان في منطقة الرسم لوضع الطاولة

### العرض في التطبيق:

#### العرض ثنائي الأبعاد:
- **الكراسي**: تظهر كمربعات بنية مع رموز للمقعد والظهر
- **الطاولات**: تظهر كمستطيلات بنية فاتحة مع خطوط داخلية
- **النصوص**: تعرض أبعاد الكائنات أسفل كل كائن

#### العرض ثلاثي الأبعاد:
- **الكراسي**:
  - **نموذج واقعي**: يستخدم ملف `chair.obj` لعرض كرسي ثلاثي الأبعاد واقعي
  - **نموذج بديل**: في حالة عدم وجود الملف، يتم إنشاء كرسي بسيط مع:
    - مقعد أفقي
    - ظهر عمودي
    - أربعة أرجل
- **الطاولات**: نماذج ثلاثية الأبعاد كاملة مع:
  - سطح طاولة أفقي
  - حواف جانبية
  - أربعة أرجل

### الحفظ والتحميل:
- يتم حفظ الكراسي والطاولات في ملف JSON منفصل
- يتم تحميلها تلقائياً عند فتح التصميم
- تظهر في العرض ثلاثي الأبعاد مع الجدران

### الحذف:
- يمكن حذف الكراسي والطاولات باستخدام أداة الحذف
- انقر على الكائن المراد حذفه بعد اختيار أداة الحذف

### نصائح للاستخدام:
1. **التخطيط**: ضع الجدران أولاً، ثم أضف الأثاث
2. **المقياس**: تأكد من أن أحجام الأثاث مناسبة لحجم الغرفة
3. **التنظيم**: استخدم العرض ثلاثي الأبعاد للتحقق من التخطيط النهائي
4. **الحفظ**: احفظ عملك بانتظام باستخدام زر الحفظ

### مثال على الاستخدام:
1. ارسم جدران الغرفة
2. أضف كرسي بحجم 0.6 متر في زاوية الغرفة
3. أضف طاولة بأبعاد 1.5x2.0 متر في وسط الغرفة
4. استخدم العرض ثلاثي الأبعاد لمراجعة التصميم
5. احفظ التصميم

### الملفات المضافة:
- `chair_icon.png`: أيقونة الكرسي المحدثة (تشير إلى النموذج ثلاثي الأبعاد)
- `table_icon.png`: أيقونة الطاولة
- `create_icons.py`: سكريبت إنشاء الأيقونات
- `test_furniture.json`: ملف اختبار يحتوي على غرفة مع أثاث

### الملفات المستخدمة:
- `chair.obj`: نموذج الكرسي ثلاثي الأبعاد الواقعي (موجود مسبقاً)

### التحديثات في الكود:
- إضافة دعم الكائنات الجديدة في `main.py`
- إضافة دوال العرض ثلاثي الأبعاد في `view_3d.py`
- تحديث نظام الحفظ والتحميل لدعم الأثاث
- تحديث دالة `create_3d_chair` لاستخدام ملف `chair.obj`
- إضافة دالة بديلة `create_simple_chair_fallback` في حالة عدم وجود الملف

### ملاحظات تقنية:
- يتم تحميل نموذج الكرسي من ملف `chair.obj` تلقائياً
- في حالة فشل التحميل، يتم إنشاء كرسي بسيط كبديل
- يمكن تخصيص حجم الكرسي من خلال حقل الإدخال (0.1 - 2.0 متر)
- النموذج يدعم التكبير والتصغير حسب الحجم المطلوب
- تم تحسين المقياس ليكون متناسق مع الجدران (مقسوم على 20)
- **الكرسي يجلس على الشبكة**: المقعد أمامك والأرجل على الأرض
- الحجم الافتراضي الجديد: 0.5 متر للحصول على حجم مناسب
- **تحريك تفاعلي**: يمكن تحريك الكراسي إلى أي مكان بالماوس

### 🎮 ميزة تحريك الكراسي الجديدة:
- **النقر للتحديد**: انقر على أي كرسي لتحديده (سيصبح أصفر)
- **السحب للتحريك**: اسحب الكرسي إلى أي مكان على الشبكة
- **الإفلات للوضع**: اترك الكرسي في المكان المطلوب
- **حرية كاملة**: يمكن وضع الكرسي في أي مكان داخل أو خارج الجدران
- **تحديث فوري**: الموضع الجديد يظهر فوراً في العرض ثلاثي الأبعاد

### 🎨 تحسينات المظهر:
- **إخفاء المحاور**: تم إخفاء الخطوط الحمراء والخضراء والزرقاء نهائياً
- **جدران عملاقة**: جدران بسمك 60 سم وارتفاع 6 أمتار (عالية جداً!)
- **6 وجوه كاملة**: كل جدار له 6 وجوه (أمامي، خلفي، يسار، يمين، علوي، سفلي)
- **ألوان فاتحة وواضحة**: ألوان أبيض ورمادي فاتح لوضوح أكبر
- **مواد محسنة**: إضاءة محيطة أقوى وانعكاس محسن
- **إضاءة قوية**: 4 مصادر إضاءة قوية لإظهار السمك والعمق
- **حل مشكلة الدوران**: الوجوه مرئية من جميع الزوايا
- **عدم إزالة الوجوه الخلفية**: حل مشكلة الإطار الأسود عند الدوران
- **🚫 إزالة الخطوط السوداء**: مظهر نظيف تماماً بدون أي حدود أو خطوط
- **مظهر معماري نظيف**: يبدو مثل الجدران الحقيقية العالية والنظيفة

### 🏗️ التفاصيل التقنية للجدار ثلاثي الأبعاد:

#### **البنية الهندسية:**
```
الجدار الواحد يتكون من:
├── الوجه الأمامي: أبيض فاتح جداً (95%) + لمعان عالي + مرئي من الجهتين
├── الوجه الخلفي: أبيض مائل للرمادي (90%) + لمعان متوسط + مرئي من الجهتين
├── الوجه الأيسر: رمادي فاتح (85%) + لمعان منخفض + مرئي من الجهتين
├── الوجه الأيمن: رمادي فاتح (85%) + لمعان منخفض + مرئي من الجهتين
├── الوجه العلوي: أبيض تقريباً (98%) + لمعان عالي جداً + مرئي من الجهتين
└── الوجه السفلي: رمادي متوسط (80%) + لمعان منخفض + مرئي من الجهتين
```

#### **نظام الإضاءة:**
- **إضاءة محيطة**: 50% شدة للإضاءة العامة
- **إضاءة رئيسية**: من الأمام والأعلى (90% شدة)
- **إضاءة ثانوية**: من اتجاه مختلف (60% شدة)
- **إضاءة جانبية**: لإظهار السمك (40% شدة)

#### **المواصفات النهائية المحسنة:**
- **السمك**: 60 سم (0.6 متر) - سمك جدار كبير وواضح جداً
- **الارتفاع**: 6.0 متر - عالي جداً مثل الجدران الحقيقية
- **المواد**: مواد محسنة مع إضاءة محيطة أقوى (60%)
- **التظليل**: ناعم ومتدرج بين الوجوه
- **العرض**: حل مشكلة الإطار الأسود عند الدوران
- **الرؤية**: جميع الوجوه مرئية من جميع الزوايا
- **الألوان**: فاتحة وواضحة للرؤية الأفضل
- **🚫 بدون خطوط**: إزالة تامة للخطوط السوداء والحدود
- **مظهر نظيف**: سطح أملس بدون أي تشويش بصري

### نصائح للحجم المناسب:
- **0.4-0.5 متر**: كراسي عادية للطعام والمطبخ
- **0.6-0.7 متر**: كراسي مكتب أو دراسة
- **0.8-1.0 متر**: كراسي استرخاء أو صالة
