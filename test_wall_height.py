#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لمشكلة ارتفاع الجدران في العرض ثلاثي الأبعاد
"""

import json
import os

def create_test_design():
    """إنشاء ملف تصميم اختباري مع جدران بارتفاعات مختلفة"""

    # إنشاء بيانات اختبارية - غرفة مستطيلة مع جدران بارتفاعات مختلفة
    test_data = {
        "walls": [
            # الجدار الأمامي (الجنوبي)
            {
                "start_m": [0.0, 0.0],
                "end_m": [6.0, 0.0],
                "length_m": 6.0,
                "height_m": 3.0
            },
            # الجدار الأيمن (الشرقي)
            {
                "start_m": [6.0, 0.0],
                "end_m": [6.0, 4.0],
                "length_m": 4.0,
                "height_m": 3.0
            },
            # الجدار الخلفي (الشمالي) - مع نوافذ
            {
                "start_m": [6.0, 4.0],
                "end_m": [0.0, 4.0],
                "length_m": 6.0,
                "height_m": 3.0
            },
            # الجدار الأيسر (الغربي)
            {
                "start_m": [0.0, 4.0],
                "end_m": [0.0, 0.0],
                "length_m": 4.0,
                "height_m": 3.0
            },
            # جدار داخلي فاصل
            {
                "start_m": [2.0, 0.0],
                "end_m": [2.0, 2.0],
                "length_m": 2.0,
                "height_m": 2.5
            }
        ],
        "measurements": []
    }

    # حفظ الملف
    test_file = "test_design.json"
    with open(test_file, "w", encoding="utf-8") as f:
        json.dump(test_data, f, indent=4, ensure_ascii=False)

    print(f"✅ تم إنشاء ملف التصميم الاختباري: {test_file}")
    print("📐 الجدران المُنشأة:")
    for i, wall in enumerate(test_data["walls"], 1):
        print(f"  🧱 جدار {i}: طول {wall['length_m']}م، ارتفاع {wall['height_m']}م")

    return test_file

def test_3d_viewer():
    """اختبار العارض ثلاثي الأبعاد"""
    try:
        # إنشاء ملف التصميم الاختباري
        test_file = create_test_design()

        # استيراد العارض ثلاثي الأبعاد
        from view_3d import launch_3d_viewer

        print("\n🚀 تشغيل العارض ثلاثي الأبعاد...")
        print("🎯 يجب أن ترى غرفة مستطيلة مع جدران واضحة:")
        print("   • 4 جدران خارجية: ارتفاع 3.0 متر")
        print("   • جدار داخلي فاصل: ارتفاع 2.5 متر")
        print("   • جدران رمادية اللون مع حواف واضحة")
        print("   • سمك الجدران: 0.25 متر")
        print("=" * 50)

        # تشغيل العارض
        launch_3d_viewer(test_file)

        # تنظيف الملف الاختباري
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑️ تم حذف الملف الاختباري: {test_file}")

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 اختبار مشكلة ارتفاع الجدران في العرض ثلاثي الأبعاد")
    print("=" * 60)
    test_3d_viewer()
