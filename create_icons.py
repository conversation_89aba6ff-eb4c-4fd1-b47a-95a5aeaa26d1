import pygame
import os

# إنشاء أيقونات بسيطة للكراسي والطاولات
def create_chair_icon():
    """إنشاء أيقونة كرسي واقعية تعكس استخدام نموذج OBJ"""
    size = (40, 40)
    surface = pygame.Surface(size, pygame.SRCALPHA)

    # لون بني للكرسي
    chair_color = (139, 69, 19)
    dark_brown = (101, 67, 33)
    light_brown = (160, 100, 50)

    # رسم ظهر الكرسي بشكل أكثر واقعية
    pygame.draw.rect(surface, chair_color, (14, 4, 12, 18))
    pygame.draw.rect(surface, light_brown, (15, 5, 10, 16), 1)

    # رسم مقعد الكرسي مع منظور
    pygame.draw.polygon(surface, chair_color, [(8, 20), (32, 20), (30, 26), (10, 26)])
    pygame.draw.polygon(surface, dark_brown, [(8, 20), (32, 20), (30, 26), (10, 26)], 1)

    # رسم أرجل الكرسي الأربعة
    pygame.draw.rect(surface, dark_brown, (10, 26, 2, 12))
    pygame.draw.rect(surface, dark_brown, (28, 26, 2, 12))
    pygame.draw.rect(surface, dark_brown, (12, 28, 2, 10))
    pygame.draw.rect(surface, dark_brown, (26, 28, 2, 10))

    # إضافة تفاصيل للكرسي
    pygame.draw.line(surface, dark_brown, (16, 8), (24, 8), 1)
    pygame.draw.line(surface, dark_brown, (16, 12), (24, 12), 1)
    pygame.draw.line(surface, dark_brown, (16, 16), (24, 16), 1)

    # إضافة حدود خارجية
    pygame.draw.rect(surface, (0, 0, 0), (14, 4, 12, 18), 1)

    # إضافة نص "3D" صغير للإشارة إلى النموذج ثلاثي الأبعاد
    font = pygame.font.Font(None, 12)
    text = font.render("3D", True, (255, 255, 255))
    surface.blit(text, (2, 2))

    return surface

def create_table_icon():
    """إنشاء أيقونة طاولة بسيطة"""
    size = (40, 40)
    surface = pygame.Surface(size, pygame.SRCALPHA)

    # لون بني فاتح للطاولة
    table_color = (160, 82, 45)
    dark_brown = (101, 67, 33)

    # رسم سطح الطاولة
    pygame.draw.rect(surface, table_color, (5, 15, 30, 12))

    # رسم أرجل الطاولة
    pygame.draw.rect(surface, dark_brown, (7, 27, 3, 10))
    pygame.draw.rect(surface, dark_brown, (30, 27, 3, 10))
    pygame.draw.rect(surface, dark_brown, (7, 27, 3, 10))
    pygame.draw.rect(surface, dark_brown, (30, 27, 3, 10))

    # إضافة حدود
    pygame.draw.rect(surface, (0, 0, 0), (5, 15, 30, 12), 1)

    # رسم خطوط لتمثيل سطح الطاولة
    pygame.draw.line(surface, (0, 0, 0), (8, 18), (32, 18), 1)
    pygame.draw.line(surface, (0, 0, 0), (8, 24), (32, 24), 1)

    return surface

def main():
    """إنشاء ملفات الأيقونات"""
    pygame.init()

    # إنشاء أيقونة الكرسي
    chair_icon = create_chair_icon()
    pygame.image.save(chair_icon, "chair_icon.png")
    print("تم إنشاء أيقونة الكرسي: chair_icon.png")

    # إنشاء أيقونة الطاولة
    table_icon = create_table_icon()
    pygame.image.save(table_icon, "table_icon.png")
    print("تم إنشاء أيقونة الطاولة: table_icon.png")

    pygame.quit()
    print("تم إنشاء جميع الأيقونات بنجاح!")

if __name__ == "__main__":
    main()
